{"name": "web-sonar-discover", "version": "0.1.0", "scripts": {"build:css": "postcss src/styles.css -o dist/styles.css", "build:ts": "tsc", "copy:assets": "cp src/index.html dist/", "build": "npm run build:css && npm run build:ts && npm run copy:assets", "start": "npm run build && electron dist/main.js", "dev": "npm run build && electron dist/main.js", "clean": "<PERSON><PERSON><PERSON> dist", "pack": "electron-builder", "dist": "electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "Selladore", "license": "proprietary", "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.1.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "postcss-cli": "^11.0.1", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "main": "dist/main.js", "dependencies": {"boolean": "^3.2.0", "buffer-crc32": "^0.2.13", "cacheable-lookup": "^5.0.4", "cacheable-request": "^7.0.4", "clone-response": "^1.0.3", "debug": "^4.4.1", "decompress-response": "^6.0.0", "defer-to-connect": "^2.0.1", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "detect-node": "^2.1.0", "end-of-stream": "^1.4.5", "env-paths": "^2.2.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es6-error": "^4.1.1", "escape-string-regexp": "^4.0.0", "extract-zip": "^2.0.1", "fd-slicer": "^1.1.0", "fs-extra": "^8.1.0", "get-stream": "^5.2.0", "global-agent": "^3.0.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "got": "^11.8.6", "graceful-fs": "^4.2.11", "has-property-descriptors": "^1.0.2", "http-cache-semantics": "^4.2.0", "http2-wrapper": "^1.0.3", "json-buffer": "^3.0.1", "json-stringify-safe": "^5.0.1", "jsonfile": "^4.0.0", "keyv": "^4.5.4", "lowercase-keys": "^2.0.0", "matcher": "^3.0.0", "mimic-response": "^1.0.1", "ms": "^2.1.3", "normalize-url": "^6.1.0", "object-keys": "^1.1.1", "once": "^1.4.0", "p-cancelable": "^2.1.1", "pend": "^1.2.0", "progress": "^2.0.3", "pump": "^3.0.3", "quick-lru": "^5.1.1", "resolve-alpn": "^1.2.1", "responselike": "^2.0.1", "roarr": "^2.15.4", "semver": "^6.3.1", "semver-compare": "^1.0.0", "serialize-error": "^7.0.1", "sprintf-js": "^1.1.3", "sumchecker": "^3.0.1", "type-fest": "^0.13.1", "undici-types": "^6.21.0", "universalify": "^0.1.2", "wrappy": "^1.0.2", "yauzl": "^2.10.0"}, "keywords": [], "description": ""}