@import "tailwindcss";

/* Dark theme root variables */
:root {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --accent-primary: #3b82f6;
  --accent-secondary: #1e40af;
  --border-color: #4b5563;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Tiles grid layout */
.tiles-grid {
  grid-template-columns: 1fr;
}

.tiles-grid.has-1 {
  grid-template-columns: 1fr;
}

.tiles-grid.has-2 {
  grid-template-columns: 1fr 1fr;
}

.tiles-grid.has-3 {
  grid-template-columns: 1fr 1fr;
}

.tiles-grid.has-4 {
  grid-template-columns: 1fr 1fr;
}

.tiles-grid.has-many {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Tile styling */
.sonar-tile {
  background: linear-gradient(145deg, #374151, #4b5563);
  border: 1px solid #4b5563;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.tile-header {
  background: rgba(31, 41, 55, 0.95);
  border-bottom: 1px solid #4b5563;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tile-content {
  flex: 1;
  position: relative;
  background: #111827;
}

.tile-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.tile-loading {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #111827;
}

.tile-error {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #111827;
  flex-direction: column;
}

/* Custom styles for split-view layout */
@media (max-width: 768px) {
  #devicePanel {
    width: 100% !important;
  }
  
  #tilesPanel {
    width: 100% !important;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
  }
  
  #tilesPanel.mobile-show {
    display: flex !important;
  }
  
  .tiles-grid {
    grid-template-columns: 1fr !important;
  }
}

/* Ensure tiles panel is completely hidden when not connected */
#tilesPanel.hidden {
  display: none !important;
}

/* Compact device list styles - only when connected */
.connected #devicePanel {
  min-width: 250px;
  max-width: 300px;
}

@media (min-width: 1024px) {
  .connected #devicePanel {
    width: 280px !important;
  }
  
  .connected #tilesPanel {
    width: calc(100% - 280px) !important;
  }
}

/* Smooth transitions for panel resizing */
#devicePanel, #tilesPanel {
  transition: width 0.3s ease-in-out;
}

/* Iframe loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.iframe-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom scrollbar for device panel - Dark Theme */
#devicePanel::-webkit-scrollbar {
  width: 6px;
}

#devicePanel::-webkit-scrollbar-track {
  background: #1f2937;
}

#devicePanel::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
}

#devicePanel::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Modern dark theme enhancements */
.device-card {
  background: linear-gradient(145deg, #374151, #4b5563);
  border: 1px solid #4b5563;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.device-card:hover {
  background: linear-gradient(145deg, #4b5563, #6b7280);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.device-card.connected {
  background: linear-gradient(145deg, #1e40af, #3b82f6);
  border-color: #3b82f6;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Status indicators */
.status-online {
  background: #10b981;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-offline {
  background: #6b7280;
}

/* Enhanced button styling */
#refreshBtn:hover {
  box-shadow: 0 8px 15px rgba(37, 99, 235, 0.3);
  transform: translateY(-1px);
}

/* Glassmorphism effect for panels */
#devicePanel {
  backdrop-filter: blur(10px);
  background: rgba(31, 41, 55, 0.95);
}

#tilesPanel .bg-gray-800 {
  backdrop-filter: blur(10px);
  background: rgba(31, 41, 55, 0.95);
}

/* Modern loading animation */
@keyframes modernPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.iframe-loading {
  animation: modernPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}